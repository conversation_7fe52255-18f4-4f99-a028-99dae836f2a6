# 🚀 Redis Stream & Pub/Sub 实时演示

一个简化的演示项目，展示Redis Stream和Pub/Sub的强大实时处理能力，结合ClickHouse大数据查询和WebSocket实时推送。

## ✨ 功能特点

### 🔥 Redis核心功能演示
- **Redis Stream**: 持久化数据流处理，支持消费者组
- **Redis Pub/Sub**: 实时消息广播和订阅
- **Redis Hash**: 高效数据缓存
- **Redis Sorted Set**: 实时排行榜

### 📊 数据处理流程
1. **ClickHouse查询**: 从400万+强制平仓记录中查询实时统计
2. **Redis Stream**: 将数据推送到持久化流中
3. **Redis Pub/Sub**: 广播数据更新消息
4. **WebSocket**: 实时推送到前端页面
5. **实时展示**: 毫秒级数据可视化更新

### 🎯 技术架构
- **后端**: Node.js + Express + Socket.IO
- **前端**: 原生HTML + CSS + JavaScript
- **数据库**: ClickHouse (云服务器)
- **缓存**: Redis (本地)
- **通信**: WebSocket实时双向通信

## 🚀 快速启动

### 前置要求
1. **Node.js** (v16+)
2. **Redis服务器** (localhost:6379)
3. **网络连接** (访问ClickHouse云服务器)

### 启动步骤

```bash
# 1. 进入项目目录
cd simple-demo

# 2. 启动项目 (自动安装依赖)
node start.js

# 3. 打开浏览器访问
http://localhost:3002
```

### 手动启动 (可选)
```bash
# 安装依赖
cd backend
npm install

# 启动服务器
npm start
```

## 📱 界面功能

### 🎛️ 实时状态栏
- 总订单数 (最近1小时)
- 平均交易数量
- 活跃交易对数量
- 实时更新时间

### 📊 数据展示区域

#### 1. 实时统计面板
- 买单/卖单数量对比
- 总交易量统计
- 数据源标识

#### 2. 热门交易对排行榜
- 基于Redis Sorted Set实现
- 实时订单数量排序
- 平均价格和交易量

#### 3. 实时强制平仓流
- 基于Redis Stream实现
- 最新强制平仓记录
- 买卖方向标识
- 实时动画效果

## 🔧 API接口

### 健康检查
```
GET /api/health
```

### 实时统计
```
GET /api/stats
```

### 热门交易对
```
GET /api/symbols
```

### 最新强制平仓
```
GET /api/liquidations
```

## 🎯 Redis功能展示

### 1. Redis Stream
```javascript
// 数据推送到Stream
await redis.xadd('liquidation:stream', '*', 
  'data', JSON.stringify(data),
  'timestamp', Date.now()
);

// 创建消费者组
await redis.xgroup('CREATE', 'liquidation:stream', 'demo-group', '$');
```

### 2. Redis Pub/Sub
```javascript
// 发布消息
await redisPub.publish('liquidation:updates', JSON.stringify(data));

// 订阅消息
redis.subscribe('liquidation:updates');
```

### 3. 实时数据流
```
ClickHouse → Redis Stream → Redis Pub/Sub → WebSocket → 前端页面
```

## 📈 数据源

### ClickHouse数据库
- **服务器**: ***********:8123
- **数据表**: force_orders
- **记录数**: 4,000,000+ 条强制平仓记录
- **更新频率**: 实时查询

### 数据字段
- `symbol`: 交易对 (如 BTCUSDT)
- `side`: 买卖方向 (BUY/SELL)
- `quantity`: 交易数量
- `price`: 交易价格
- `timestamp`: 时间戳

## 🔄 实时更新机制

1. **定时查询**: 每5秒查询ClickHouse最新数据
2. **Stream推送**: 数据推送到Redis Stream持久化存储
3. **Pub/Sub广播**: 同时广播到所有订阅者
4. **WebSocket推送**: 实时推送到所有连接的客户端
5. **前端更新**: 页面实时更新数据和动画

## 🎨 界面特色

- **渐变背景**: 现代化视觉设计
- **毛玻璃效果**: 半透明卡片设计
- **实时动画**: 数据更新动画效果
- **响应式布局**: 适配不同屏幕尺寸
- **状态指示**: 连接状态实时显示

## 🛠️ 故障排除

### Redis连接失败
```bash
# 启动Redis服务器
redis-server

# 检查Redis状态
redis-cli ping
```

### ClickHouse连接失败
- 检查网络连接
- 确认服务器地址和端口
- 验证用户名和密码

### 端口占用
```bash
# 检查端口占用
netstat -ano | findstr :3002

# 修改端口 (在server.js中)
const PORT = process.env.PORT || 3003;
```

## 📝 开发说明

### 项目结构
```
simple-demo/
├── backend/           # 后端服务器
│   ├── server.js     # 主服务器文件
│   └── package.json  # 依赖配置
├── frontend/         # 前端页面
│   └── index.html    # 主页面
├── start.js          # 启动脚本
└── README.md         # 说明文档
```

### 扩展功能
- 添加更多Redis数据结构演示
- 集成更多图表可视化
- 添加用户交互功能
- 实现数据持久化存储

---

🎉 **享受Redis的强大实时处理能力！**
