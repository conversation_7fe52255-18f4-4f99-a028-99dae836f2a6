/**
 * Redis演示项目启动器
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 启动Redis Stream & Pub/Sub演示项目');
console.log('=' .repeat(50));

// 检查依赖
function checkDependencies() {
  const packagePath = path.join(__dirname, 'backend', 'package.json');
  const nodeModulesPath = path.join(__dirname, 'backend', 'node_modules');
  
  if (!fs.existsSync(nodeModulesPath)) {
    console.log('📦 正在安装依赖...');
    
    const npm = spawn('npm', ['install'], {
      cwd: path.join(__dirname, 'backend'),
      stdio: 'inherit',
      shell: true
    });
    
    npm.on('close', (code) => {
      if (code === 0) {
        console.log('✅ 依赖安装完成');
        startServer();
      } else {
        console.error('❌ 依赖安装失败');
        process.exit(1);
      }
    });
  } else {
    console.log('✅ 依赖已存在，直接启动服务器');
    startServer();
  }
}

// 启动服务器
function startServer() {
  console.log('\n🚀 启动后端服务器...');
  
  const server = spawn('node', ['server.js'], {
    cwd: path.join(__dirname, 'backend'),
    stdio: 'inherit',
    shell: true
  });
  
  server.on('close', (code) => {
    console.log(`\n🛑 服务器已停止 (退出码: ${code})`);
  });
  
  // 优雅关闭
  process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭服务器...');
    server.kill('SIGINT');
    process.exit(0);
  });
}

// 显示使用说明
function showInstructions() {
  console.log('\n📋 使用说明:');
  console.log('1. 确保Redis服务器运行在 localhost:6379');
  console.log('2. 确保ClickHouse服务器可访问 (***********:8123)');
  console.log('3. 服务器启动后访问: http://localhost:3002');
  console.log('4. 按 Ctrl+C 停止服务器');
  console.log('\n🔥 Redis功能演示:');
  console.log('• Redis Stream: 数据流处理和消费者组');
  console.log('• Redis Pub/Sub: 实时消息广播');
  console.log('• WebSocket: 实时数据推送到前端');
  console.log('• ClickHouse: 大数据查询和分析');
  console.log('=' .repeat(50));
}

// 主函数
function main() {
  showInstructions();
  checkDependencies();
}

// 启动
main();
